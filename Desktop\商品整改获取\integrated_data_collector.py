#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成数据收集器
结合原始1.01.py的数据获取功能和SQLite数据库存储
"""

import re
import requests
import execjs
import os
import json
import csv
import sqlite3
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlite_demo import RectificationDatabase

class IntegratedDataCollector:
    """集成数据收集器类"""
    
    def __init__(self, db_path: str = "rectification_data.db"):
        """
        初始化数据收集器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db = RectificationDatabase(db_path)
        self.json_data = None
        
    def initialize(self):
        """初始化数据库和获取配置数据"""
        try:
            # 连接数据库
            self.db.connect()
            self.db.create_tables()
            
            # 获取cookie配置数据
            response = requests.get('http://121.43.34.187:9000/api/data1')
            if response.status_code == 200:
                self.json_data = response.json().get('data')
                print(f"✅ 成功获取配置数据，包含 {len(self.json_data)} 个店铺配置")
            else:
                print(f"❌ 获取配置数据失败: HTTP {response.status_code}")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False
    
    def get_cookie_from_cookie3(self, shop_name):
        """从配置数据中获取cookie"""
        if shop_name in self.json_data:
            cookie_list = self.json_data[shop_name]
            if isinstance(cookie_list, list):
                return '; '.join(cookie_list)
            elif isinstance(cookie_list, str):
                return cookie_list
            else:
                raise ValueError(f"Unexpected type for cookie: {type(cookie_list)}")
        return None

    def fetch_balance_info(self, shop_name):
        """获取店铺整改信息"""
        try:
            cookie_value1 = self.get_cookie_from_cookie3(shop_name)
            if not cookie_value1:
                return (shop_name, None, None, "Cookie not found")

            headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'anti-content': requests.get('http://121.43.34.187:9000/api/0as').json().get('data'),
                'cache-control': 'max-age=0',
                'priority': 'u=1, i',
                'referer': 'https://mms.pinduoduo.com/aftersales/rectification',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'cookie': cookie_value1,
            }

            response = requests.get('https://mms.pinduoduo.com/api/colombo/rectification/list', headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                return (shop_name, data, None, "Success")
            else:
                return (shop_name, None, None, f"HTTP Error: {response.status_code}")

        except Exception as e:
            return (shop_name, None, None, str(e))
    
    def collect_and_store_data(self, shop_names, max_workers=10):
        """
        收集并存储数据
        
        Args:
            shop_names: 店铺名称列表
            max_workers: 最大并发数
        """
        print(f"🚀 开始收集 {len(shop_names)} 个店铺的数据...")
        
        success_count = 0
        error_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_shop = {
                executor.submit(self.fetch_balance_info, shop_name): shop_name
                for shop_name in shop_names
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_shop):
                shop_name = future_to_shop[future]
                try:
                    result = future.result()
                    shop_name, data, error, status = result
                    
                    if data and status == "Success":
                        # 存储到数据库
                        self.db.insert_shop_data(shop_name, data)
                        success_count += 1
                        print(f"✅ {shop_name}: 数据收集并存储成功")
                    else:
                        error_count += 1
                        print(f"❌ {shop_name}: {status}")
                        
                except Exception as e:
                    error_count += 1
                    print(f"❌ {shop_name}: 处理异常 - {e}")
        
        print(f"\n📊 数据收集完成:")
        print(f"   成功: {success_count} 个店铺")
        print(f"   失败: {error_count} 个店铺")
        
        return success_count, error_count
    
    def generate_report(self):
        """生成数据报告"""
        print("\n" + "="*60)
        print("📋 数据收集报告")
        print("="*60)
        
        # 显示店铺汇总
        self.db.query_shop_summary()
        
        # 显示统计信息
        self.db.get_statistics()
        
        # 显示最新的整改项目
        print(f"\n📋 最新整改项目 (前10条):")
        self.db.query_rectification_items(limit=10)
    
    def cleanup(self):
        """清理资源"""
        if self.db:
            self.db.disconnect()
            print("✅ 数据库连接已关闭")


def load_shop_names_from_csv(file_path):
    """从CSV文件加载店铺名称"""
    shop_names = []
    try:
        with open(file_path, mode='r', encoding='ANSI') as shop_file:
            reader = csv.reader(shop_file)
            next(reader)  # 跳过表头
            shop_names = [row[2] for row in reader if len(row) > 2]  # 获取第三列店铺名称
        print(f"✅ 从CSV文件加载了 {len(shop_names)} 个店铺名称")
    except FileNotFoundError:
        print(f"❌ CSV文件未找到: {file_path}")
        print("💡 使用示例店铺名称进行演示")
        # 使用示例数据
        shop_names = ["雅鹿旗舰店", "测试店铺A", "示例店铺B"]
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        shop_names = ["雅鹿旗舰店", "测试店铺A", "示例店铺B"]
    
    return shop_names


def main():
    """主函数"""
    print("🎯 集成数据收集器启动")
    print("="*50)
    
    # 创建数据收集器
    collector = IntegratedDataCollector("production_rectification.db")
    
    try:
        # 初始化
        if not collector.initialize():
            print("❌ 初始化失败，程序退出")
            return
        
        # 加载店铺名称
        csv_file_path = os.path.join('F:\李泽鹏\获取数据', '店铺-账号信息.csv')
        shop_names = load_shop_names_from_csv(csv_file_path)
        
        if not shop_names:
            print("❌ 没有找到店铺名称，程序退出")
            return
        
        # 收集并存储数据
        success_count, error_count = collector.collect_and_store_data(shop_names, max_workers=5)
        
        # 生成报告
        if success_count > 0:
            collector.generate_report()
        
        print(f"\n🎉 数据收集完成！")
        print(f"   数据库文件: production_rectification.db")
        print(f"   成功收集: {success_count} 个店铺")
        print(f"   失败: {error_count} 个店铺")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
    finally:
        # 清理资源
        collector.cleanup()


if __name__ == "__main__":
    main()
