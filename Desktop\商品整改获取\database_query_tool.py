#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询工具
提供简单的命令行界面来查询和分析整改数据
"""

import sqlite3
import json
from datetime import datetime
from sqlite_demo import RectificationDatabase

class DatabaseQueryTool:
    """数据库查询工具类"""
    
    def __init__(self, db_path: str = "production_rectification.db"):
        """初始化查询工具"""
        self.db = RectificationDatabase(db_path)
        self.db.connect()
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🔍 数据库查询工具")
        print("="*60)
        print("1. 查看所有店铺汇总")
        print("2. 查看特定店铺详情")
        print("3. 按问题类型统计")
        print("4. 按状态统计")
        print("5. 查看最新整改项目")
        print("6. 搜索商品名称")
        print("7. 导出数据到CSV")
        print("8. 数据库统计信息")
        print("9. 自定义SQL查询")
        print("0. 退出")
        print("-"*60)
    
    def query_all_shops(self):
        """查询所有店铺汇总"""
        self.db.query_shop_summary()
    
    def query_specific_shop(self):
        """查询特定店铺详情"""
        shop_name = input("请输入店铺名称: ").strip()
        if shop_name:
            print(f"\n🏪 店铺 '{shop_name}' 的详细信息:")
            self.db.query_shop_summary(shop_name)
            print(f"\n📋 店铺 '{shop_name}' 的整改项目:")
            self.db.query_rectification_items(shop_name=shop_name, limit=20)
        else:
            print("❌ 店铺名称不能为空")
    
    def query_by_problem_type(self):
        """按问题类型统计"""
        try:
            self.db.cursor.execute("""
                SELECT problem_label, COUNT(*) as count, 
                       COUNT(CASE WHEN status = 0 THEN 1 END) as init_count,
                       COUNT(CASE WHEN status = 1 THEN 1 END) as submit_count,
                       COUNT(CASE WHEN status = 3 THEN 1 END) as no_submit_count
                FROM rectification_items 
                WHERE problem_label IS NOT NULL
                GROUP BY problem_label
                ORDER BY count DESC
            """)
            
            results = self.db.cursor.fetchall()
            
            print(f"\n📊 按问题类型统计:")
            print("-" * 80)
            print(f"{'问题类型':<20} {'总数':<8} {'待处理':<8} {'已提交':<8} {'未提交':<8}")
            print("-" * 80)
            
            for row in results:
                print(f"{row[0]:<20} {row[1]:<8} {row[2]:<8} {row[3]:<8} {row[4]:<8}")
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")
    
    def query_by_status(self):
        """按状态统计"""
        try:
            self.db.cursor.execute("""
                SELECT status, data_category, COUNT(*) as count
                FROM rectification_items 
                GROUP BY status, data_category
                ORDER BY status, data_category
            """)
            
            results = self.db.cursor.fetchall()
            
            print(f"\n📊 按状态和类别统计:")
            print("-" * 40)
            print(f"{'状态':<8} {'类别':<12} {'数量':<8}")
            print("-" * 40)
            
            status_names = {0: "待处理", 1: "已提交", 3: "未提交"}
            
            for row in results:
                status_name = status_names.get(row[0], f"状态{row[0]}")
                print(f"{status_name:<8} {row[1]:<12} {row[2]:<8}")
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")
    
    def query_latest_items(self):
        """查看最新整改项目"""
        limit = input("请输入显示数量 (默认20): ").strip()
        try:
            limit = int(limit) if limit else 20
        except ValueError:
            limit = 20
        
        self.db.query_rectification_items(limit=limit)
    
    def search_goods_name(self):
        """搜索商品名称"""
        keyword = input("请输入商品名称关键词: ").strip()
        if not keyword:
            print("❌ 关键词不能为空")
            return
        
        try:
            self.db.cursor.execute("""
                SELECT shop_name, 
                       json_extract(base_info, '$.goodsName') as goods_name,
                       problem_label, status
                FROM rectification_items 
                WHERE json_extract(base_info, '$.goodsName') LIKE ?
                LIMIT 20
            """, (f"%{keyword}%",))
            
            results = self.db.cursor.fetchall()
            
            print(f"\n🔍 包含关键词 '{keyword}' 的商品:")
            print("-" * 100)
            print(f"{'店铺名称':<20} {'商品名称':<40} {'问题类型':<15} {'状态':<8}")
            print("-" * 100)
            
            status_names = {0: "待处理", 1: "已提交", 3: "未提交"}
            
            for row in results:
                goods_name = row[1][:35] + "..." if row[1] and len(row[1]) > 35 else (row[1] or "N/A")
                status_name = status_names.get(row[3], f"状态{row[3]}")
                print(f"{row[0]:<20} {goods_name:<40} {row[2]:<15} {status_name:<8}")
            
            if not results:
                print(f"未找到包含关键词 '{keyword}' 的商品")
                
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
    
    def export_to_csv(self):
        """导出数据到CSV"""
        import csv
        from datetime import datetime
        
        filename = f"rectification_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            self.db.cursor.execute("""
                SELECT s.shop_name, s.mall_id, s.init_counts, s.submit_counts, s.no_submit_counts,
                       r.goods_id, r.problem_label, r.status, r.data_category,
                       json_extract(r.base_info, '$.goodsName') as goods_name,
                       datetime(r.created_at_timestamp/1000, 'unixepoch') as created_time
                FROM shop_summary s
                LEFT JOIN rectification_items r ON s.shop_name = r.shop_name
                ORDER BY s.shop_name, r.created_at_timestamp DESC
            """)
            
            results = self.db.cursor.fetchall()
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入表头
                writer.writerow([
                    '店铺名称', '商户ID', '待处理数', '已提交数', '未提交数',
                    '商品ID', '问题标签', '状态', '数据类别', '商品名称', '创建时间'
                ])
                
                # 写入数据
                for row in results:
                    writer.writerow(row)
            
            print(f"✅ 数据已导出到文件: {filename}")
            print(f"   总共导出 {len(results)} 条记录")
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    
    def show_statistics(self):
        """显示数据库统计信息"""
        self.db.get_statistics()
        
        # 额外统计信息
        try:
            # 店铺问题分布
            self.db.cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN init_counts > 0 THEN 1 END) as shops_with_init,
                    COUNT(CASE WHEN submit_counts > 0 THEN 1 END) as shops_with_submit,
                    COUNT(CASE WHEN no_submit_counts > 0 THEN 1 END) as shops_with_no_submit
                FROM shop_summary
            """)
            
            result = self.db.cursor.fetchone()
            
            print(f"\n📊 店铺问题分布:")
            print(f"   有待处理问题的店铺: {result[0]}")
            print(f"   有已提交问题的店铺: {result[1]}")
            print(f"   有未提交问题的店铺: {result[2]}")
            
        except Exception as e:
            print(f"❌ 获取额外统计信息失败: {e}")
    
    def custom_sql_query(self):
        """自定义SQL查询"""
        print("\n💡 提示: 可用的表名: shop_summary, rectification_items")
        print("   示例查询: SELECT shop_name, COUNT(*) FROM rectification_items GROUP BY shop_name LIMIT 10")
        
        sql = input("\n请输入SQL查询语句: ").strip()
        if not sql:
            print("❌ SQL语句不能为空")
            return
        
        try:
            self.db.cursor.execute(sql)
            results = self.db.cursor.fetchall()
            
            if results:
                # 获取列名
                column_names = [description[0] for description in self.db.cursor.description]
                
                print(f"\n📋 查询结果 (共 {len(results)} 条):")
                print("-" * 80)
                
                # 打印表头
                header = " | ".join(f"{name:<15}" for name in column_names)
                print(header)
                print("-" * len(header))
                
                # 打印数据
                for row in results[:50]:  # 限制显示前50条
                    row_str = " | ".join(f"{str(cell):<15}" for cell in row)
                    print(row_str)
                
                if len(results) > 50:
                    print(f"... (还有 {len(results) - 50} 条记录)")
            else:
                print("查询结果为空")
                
        except Exception as e:
            print(f"❌ SQL查询失败: {e}")
    
    def run(self):
        """运行查询工具"""
        print("🎯 数据库查询工具启动")
        
        while True:
            try:
                self.show_menu()
                choice = input("请选择操作 (0-9): ").strip()
                
                if choice == '0':
                    print("👋 再见！")
                    break
                elif choice == '1':
                    self.query_all_shops()
                elif choice == '2':
                    self.query_specific_shop()
                elif choice == '3':
                    self.query_by_problem_type()
                elif choice == '4':
                    self.query_by_status()
                elif choice == '5':
                    self.query_latest_items()
                elif choice == '6':
                    self.search_goods_name()
                elif choice == '7':
                    self.export_to_csv()
                elif choice == '8':
                    self.show_statistics()
                elif choice == '9':
                    self.custom_sql_query()
                else:
                    print("❌ 无效选择，请输入 0-9")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")
                input("\n按回车键继续...")
    
    def __del__(self):
        """析构函数，确保数据库连接关闭"""
        if hasattr(self, 'db') and self.db:
            self.db.disconnect()


if __name__ == "__main__":
    tool = DatabaseQueryTool()
    tool.run()
